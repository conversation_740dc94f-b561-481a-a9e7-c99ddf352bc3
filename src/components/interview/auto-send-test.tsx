"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { useAutoSpeechFlow } from "@/hooks/use-auto-speech-flow";

/**
 * Simple test component to verify auto-send functionality
 */
export function AutoSendTest() {
  const [messageInput, setMessageInput] = useState("");
  const [isUserTurn, setIsUserTurn] = useState(true);
  const [sentMessages, setSentMessages] = useState<string[]>([]);

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      setSentMessages(prev => [...prev, messageInput]);
      setMessageInput("");
      setIsUserTurn(false);
      
      // Simulate AI response after 2 seconds
      setTimeout(() => {
        setIsUserTurn(true);
      }, 2000);
    }
  };

  const autoSpeechFlow = useAutoSpeechFlow(
    isUserTurn,
    handleSendMessage,
    {
      autoMicEnabled: true,
      autoSendEnabled: true,
      autoSendDelay: 3000, // 3 seconds for testing
      minSpeechDuration: 500, // 0.5 seconds for testing
    },
    messageInput
  );

  // Simulate speech recognition events
  const simulateSpeech = () => {
    console.log("[Test] Starting speech simulation");
    
    // Start speech
    document.dispatchEvent(new CustomEvent("speech-recognition-status", {
      detail: { isListening: true }
    }));
    
    // Add some content
    setMessageInput("Hello, this is a test message from speech recognition!");
    
    // End speech after 2 seconds
    setTimeout(() => {
      document.dispatchEvent(new CustomEvent("speech-recognition-status", {
        detail: { isListening: false }
      }));
      console.log("[Test] Speech simulation ended");
    }, 2000);
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Auto-Send Test</CardTitle>
          <CardDescription>
            Test the auto-send functionality after speech recognition
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">User Turn</div>
              <Badge variant={isUserTurn ? "default" : "secondary"}>
                {isUserTurn ? "Yes" : "No"}
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Auto Send</div>
              <Badge variant={autoSpeechFlow.isAutoSendPending ? "destructive" : "secondary"}>
                {autoSpeechFlow.isAutoSendPending 
                  ? `${autoSpeechFlow.autoSendCountdown}s` 
                  : "Inactive"}
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-sm text-muted-foreground">Messages Sent</div>
              <Badge variant="outline">
                {sentMessages.length}
              </Badge>
            </div>
          </div>

          {/* Message Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Message Content:</label>
            <Textarea
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              placeholder="Type a message or use the simulate button..."
              className="min-h-20"
            />
          </div>

          {/* Controls */}
          <div className="flex gap-2">
            <Button
              onClick={simulateSpeech}
              disabled={!isUserTurn}
              className="flex-1"
            >
              Simulate Speech Recognition
            </Button>
            <Button
              onClick={handleSendMessage}
              disabled={!messageInput.trim() || !isUserTurn}
              variant="outline"
            >
              Manual Send
            </Button>
            {autoSpeechFlow.isAutoSendPending && (
              <Button
                onClick={autoSpeechFlow.cancelAutoSend}
                variant="destructive"
                size="sm"
              >
                Cancel Auto-Send
              </Button>
            )}
          </div>

          {/* Sent Messages */}
          {sentMessages.length > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium">Sent Messages:</label>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {sentMessages.map((message, index) => (
                  <div
                    key={index}
                    className="p-2 bg-muted rounded text-sm"
                  >
                    {index + 1}. {message}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p><strong>Test Steps:</strong></p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Click "Simulate Speech Recognition" to start the test</li>
              <li>Watch the auto-send countdown (3 seconds)</li>
              <li>The message should be automatically sent when countdown reaches 0</li>
              <li>You can cancel the auto-send by clicking the cancel button</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
