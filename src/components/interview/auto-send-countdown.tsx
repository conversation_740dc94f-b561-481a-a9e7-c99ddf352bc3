"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Timer, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface AutoSendCountdownProps {
  isVisible: boolean;
  countdown: number;
  onCancel?: () => void;
}

/**
 * Global auto-send countdown component that appears in bottom right
 * This prevents duplicate countdown UIs and ensures consistent positioning
 */
export function AutoSendCountdown({
  isVisible,
  countdown,
  onCancel,
}: AutoSendCountdownProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <AnimatePresence>
      {isVisible && countdown > 0 && (
        <motion.div
          className="fixed bottom-4 right-4 z-50"
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
        >
          <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950 shadow-lg min-w-[280px]">
            <CardContent className="p-3">
              <div className="flex items-center justify-between gap-3">
                <div className="flex items-center gap-2">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  >
                    <Timer className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                  </motion.div>
                  <span className="text-sm font-medium text-orange-800 dark:text-orange-200">
                    Auto-sending in{" "}
                    <motion.span
                      key={countdown}
                      initial={{ scale: 1.2 }}
                      animate={{ scale: 1 }}
                      className="font-bold"
                    >
                      {countdown}s
                    </motion.span>
                  </span>
                </div>
                
                <div className="flex items-center gap-1">
                  {onCancel && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={onCancel}
                      className="h-7 px-2 text-xs border-orange-300 hover:bg-orange-100 dark:border-orange-700 dark:hover:bg-orange-900"
                    >
                      Cancel
                    </Button>
                  )}
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={onCancel}
                    className="h-7 w-7 p-0 text-orange-600 hover:bg-orange-100 dark:text-orange-400 dark:hover:bg-orange-900"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              
              {/* Progress bar */}
              <div className="mt-2 w-full bg-orange-200 dark:bg-orange-800 rounded-full h-1">
                <motion.div
                  className="bg-orange-500 dark:bg-orange-400 h-1 rounded-full"
                  initial={{ width: "100%" }}
                  animate={{ width: "0%" }}
                  transition={{ duration: countdown, ease: "linear" }}
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

/**
 * Hook to manage global auto-send countdown state
 * This ensures only one countdown is shown at a time across the app
 */
export function useGlobalAutoSendCountdown() {
  const [isVisible, setIsVisible] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [onCancel, setOnCancel] = useState<(() => void) | undefined>();

  const showCountdown = (initialCountdown: number, cancelCallback?: () => void) => {
    setCountdown(initialCountdown);
    setOnCancel(() => cancelCallback);
    setIsVisible(true);
  };

  const hideCountdown = () => {
    setIsVisible(false);
    setCountdown(0);
    setOnCancel(undefined);
  };

  const updateCountdown = (newCountdown: number) => {
    setCountdown(newCountdown);
    if (newCountdown <= 0) {
      hideCountdown();
    }
  };

  return {
    isVisible,
    countdown,
    onCancel,
    showCountdown,
    hideCountdown,
    updateCountdown,
  };
}
